// 攻击类型
export type AttackType = 'DDoS' | 'CC'

// 流量波动程度
export type FluctuationLevel = 'low' | 'medium' | 'high' | 'extreme'

// 攻击配置接口
export interface AttackConfig {
    // 攻击类型
    attackType: AttackType

    // CC 攻击的域名 (仅当攻击类型为 CC 时需要)
    domain?: string

    // 攻击流量峰值
    peakTraffic: number

    // 流量波动程度
    fluctuationLevel: FluctuationLevel

    // 时间配置
    chartStartTime: Date
    chartEndTime: Date
    attackStartTime: Date
    attackEndTime: Date
    peakTime: Date
}

// 流量数据点
export interface TrafficDataPoint {
    time: Date
    value: number
}

// 波动程度配置
export const FLUCTUATION_LEVELS = {
    low: {label: '低', description: '较平缓的流量变化'},
    medium: {label: '中', description: '适中的流量起伏'},
    high: {label: '高', description: '明显的流量波动'},
    extreme: {label: '极高', description: '剧烈的流量变化'}
} as const

// 攻击类型配置
export const ATTACK_TYPES = {
    DDoS: {
        label: 'DDoS 攻击',
        unit: 'Gbps',
        minValue: 0,
        maxValue: 1000,
        description: '分布式拒绝服务攻击'
    },
    CC: {
        label: 'CC 攻击',
        unit: 'RPS',
        minValue: 0,
        maxValue: 50000000,
        description: 'Challenge Collapsar 攻击'
    }
} as const
