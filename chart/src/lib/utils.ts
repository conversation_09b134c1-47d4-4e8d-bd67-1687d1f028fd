import {type ClassValue, clsx} from "clsx"
import {twMerge} from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

// 时间格式化工具函数
export function formatTime(date: Date): string {
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    })
}


// 获取默认时间配置
export function getDefaultTimeConfig() {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    // 图表开始时间：当天 00:00
    const chartStartTime = new Date(today)

    // 图表结束时间：当前时间
    const chartEndTime = new Date(now)

    // 攻击结束时间：当前时间前一小时的整点
    const attackEndTime = new Date(now)
    attackEndTime.setHours(now.getHours() - 1, 0, 0, 0)

    // 攻击开始时间：攻击结束时间前一小时
    const attackStartTime = new Date(attackEndTime)
    attackStartTime.setHours(attackEndTime.getHours() - 1)

    // 峰值时间：攻击开始时间 + 15分钟
    const peakTime = new Date(attackStartTime)
    peakTime.setMinutes(attackStartTime.getMinutes() + 15)

    return {
        chartStartTime,
        chartEndTime,
        attackStartTime,
        attackEndTime,
        peakTime
    }
}
