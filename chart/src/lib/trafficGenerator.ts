import type {AttackConfig, TrafficDataPoint} from '../types/attack'

// 波动程度系数配置 - 大幅增强差异化
const FLUCTUATION_COEFFICIENTS = {
    low: {amplitude: 0.03, frequency: 0.2, noise: 0.01},      // 非常平缓
    medium: {amplitude: 0.12, frequency: 0.6, noise: 0.05},   // 适度波动
    high: {amplitude: 0.25, frequency: 1.2, noise: 0.12},     // 明显波动
    extreme: {amplitude: 0.4, frequency: 2.0, noise: 0.2}     // 剧烈变化
}

// 生成随机噪声
function generateNoise(amplitude: number): number {
    return (Math.random() - 0.5) * 2 * amplitude
}

// 生成正弦波动
function generateSineWave(time: number, frequency: number, amplitude: number): number {
    return Math.sin(time * frequency * Math.PI / 180) * amplitude
}

// 生成 DDoS 攻击流量数据
function generateDDoSTraffic(config: AttackConfig): TrafficDataPoint[] {
    const dataPoints: TrafficDataPoint[] = []
    const {
        chartStartTime,
        chartEndTime,
        attackStartTime,
        attackEndTime,
        peakTraffic,
        fluctuationLevel,
        peakTime
    } = config

    const fluctuation = FLUCTUATION_COEFFICIENTS[fluctuationLevel]
    const attackDuration = attackEndTime.getTime() - attackStartTime.getTime()

    // 使用用户指定的峰值时间
    const userPeakTime = peakTime.getTime()

    // 生成所有时间点（包括30分钟间隔点和峰值时间点）
    const timePoints = new Set<number>()

    // 添加30分钟间隔的时间点
    const interval = 30 * 60 * 1000 // 30分钟的毫秒数
    for (let time = chartStartTime.getTime(); time <= chartEndTime.getTime(); time += interval) {
        timePoints.add(time)
    }

    // 添加峰值时间点（如果在攻击时间范围内且不重复）
    if (userPeakTime >= attackStartTime.getTime() && userPeakTime <= attackEndTime.getTime()) {
        timePoints.add(userPeakTime)
    }

    // 按时间排序
    const sortedTimes = Array.from(timePoints).sort((a, b) => a - b)

    for (const time of sortedTimes) {
        const currentTime = new Date(time)
        let value = 0 // 默认无流量

        if (time >= attackStartTime.getTime() && time <= attackEndTime.getTime()) {
            // 攻击期间的流量计算
            const attackProgress = (time - attackStartTime.getTime()) / attackDuration

            // 如果是峰值时间点，直接设置为峰值
            if (time === userPeakTime) {
                value = peakTraffic
            } else {
                // DDoS 特征：快速上升到峰值，然后逐渐下降
                let baseValue: number

                if (time <= userPeakTime) {
                    // 攻击开始到峰值时间：快速上升
                    if (attackProgress <= 0.05) {
                        // 前5%时间：从0开始缓慢上升
                        baseValue = peakTraffic * (attackProgress / 0.05) * 0.2
                    } else {
                        // 5%到峰值时间：快速上升到峰值
                        const timeToPeak = userPeakTime - attackStartTime.getTime()
                        const timeFromStart = time - attackStartTime.getTime()
                        const progressToPeak = Math.min(1, timeFromStart / timeToPeak)
                        baseValue = peakTraffic * 0.2 + peakTraffic * 0.8 * progressToPeak
                    }
                } else {
                    // 峰值时间之后：逐渐下降
                    const declineProgress = (time - userPeakTime) / (attackEndTime.getTime() - userPeakTime)
                    baseValue = peakTraffic * (1 - declineProgress * 0.8)
                }

                // 添加波动
                const timeInMinutes = (time - attackStartTime.getTime()) / (60 * 1000)
                const waveValue = generateSineWave(timeInMinutes, fluctuation.frequency, fluctuation.amplitude)
                const noiseValue = generateNoise(fluctuation.noise)

                // 应用波动
                const fluctuatedValue = baseValue * (1 + waveValue + noiseValue)

                // 严格限制：不能超过峰值，不能低于0
                value = Math.round(Math.min(peakTraffic, Math.max(0, fluctuatedValue)))
            }
        }
        // 攻击时间段外保持0流量（正常请求不会触发防护墙规则）

        dataPoints.push({
            time: currentTime,
            value: Math.round(value)
        })
    }

    return dataPoints
}

// 生成 CC 攻击流量数据
function generateCCTraffic(config: AttackConfig): TrafficDataPoint[] {
    const dataPoints: TrafficDataPoint[] = []
    const {
        chartStartTime,
        chartEndTime,
        attackStartTime,
        attackEndTime,
        peakTraffic,
        fluctuationLevel,
        peakTime
    } = config

    const fluctuation = FLUCTUATION_COEFFICIENTS[fluctuationLevel]
    const attackDuration = attackEndTime.getTime() - attackStartTime.getTime()

    // 使用用户指定的峰值时间
    const userPeakTime = peakTime.getTime()

    // 生成所有时间点（包括30分钟间隔点和峰值时间点）
    const timePoints = new Set<number>()

    // 添加30分钟间隔的时间点
    const interval = 30 * 60 * 1000 // 30分钟的毫秒数
    for (let time = chartStartTime.getTime(); time <= chartEndTime.getTime(); time += interval) {
        timePoints.add(time)
    }

    // 添加峰值时间点（如果在攻击时间范围内且不重复）
    if (userPeakTime >= attackStartTime.getTime() && userPeakTime <= attackEndTime.getTime()) {
        timePoints.add(userPeakTime)
    }

    // 按时间排序
    const sortedTimes = Array.from(timePoints).sort((a, b) => a - b)

    for (const time of sortedTimes) {
        const currentTime = new Date(time)
        let value = 0 // 默认无流量

        if (time >= attackStartTime.getTime() && time <= attackEndTime.getTime()) {
            // 攻击期间的流量计算
            const attackProgress = (time - attackStartTime.getTime()) / attackDuration

            // 如果是峰值时间点，直接设置为峰值
            if (time === userPeakTime) {
                value = peakTraffic
            } else {
                // CC 特征：逐渐增长到峰值，然后保持较长时间
                let baseValue: number

                if (time <= userPeakTime) {
                    // 攻击开始到峰值时间：逐渐增长（CC攻击特征是缓慢增长）
                    if (attackProgress <= 0.1) {
                        // 前10%时间：从0开始缓慢增长
                        baseValue = peakTraffic * (attackProgress / 0.1) * 0.15
                    } else {
                        // 10%到峰值时间：持续增长到峰值
                        const timeToPeak = userPeakTime - attackStartTime.getTime()
                        const timeFromStart = time - attackStartTime.getTime()
                        const progressToPeak = Math.min(1, timeFromStart / timeToPeak)
                        baseValue = peakTraffic * 0.15 + peakTraffic * 0.85 * progressToPeak
                    }
                } else {
                    // 峰值时间之后：保持高位较长时间，然后缓慢下降
                    const timeAfterPeak = time - userPeakTime
                    const remainingTime = attackEndTime.getTime() - userPeakTime
                    const declineProgress = timeAfterPeak / remainingTime

                    if (declineProgress <= 0.7) {
                        // 峰值后70%时间保持高位
                        baseValue = peakTraffic * (0.9 + 0.1 * Math.random())
                    } else {
                        // 最后30%时间缓慢下降
                        const finalDecline = (declineProgress - 0.7) / 0.3
                        baseValue = peakTraffic * (0.9 - finalDecline * 0.85)
                    }
                }

                // 添加波动（CC攻击波动相对较小）
                const timeInMinutes = (time - attackStartTime.getTime()) / (60 * 1000)
                const waveValue = generateSineWave(timeInMinutes, fluctuation.frequency * 0.8, fluctuation.amplitude * 0.6)
                const noiseValue = generateNoise(fluctuation.noise * 0.5)

                // 应用波动
                const fluctuatedValue = baseValue * (1 + waveValue + noiseValue)

                // 严格限制：不能超过峰值，不能低于0
                value = Math.round(Math.min(peakTraffic, Math.max(0, fluctuatedValue)))
            }
        }
        // 攻击时间段外保持0流量（正常请求不会触发防护墙规则）

        dataPoints.push({
            time: currentTime,
            value: Math.round(value)
        })
    }

    return dataPoints
}

// 主要的流量数据生成函数
export function generateTrafficData(config: AttackConfig): TrafficDataPoint[] {
    if (config.attackType === 'DDoS') {
        return generateDDoSTraffic(config)
    } else {
        return generateCCTraffic(config)
    }
}

// 计算 Y 轴的刻度范围 - 采用业内标准方式
export function calculateYAxisRange(data: TrafficDataPoint[], attackType: 'DDoS' | 'CC'): {
    min: number
    max: number
    step: number
    unit: string
} {
    const maxValue = Math.max(...data.map(d => d.value))

    // 业内标准：Y轴最大值设为数据最大值的 1.1-1.2 倍，确保图表美观且能完整显示峰值
    const yAxisMax = Math.ceil(maxValue * 1.15)

    if (attackType === 'DDoS') {
        const unit = 'Gbps'
        let max: number
        let step: number

        if (yAxisMax <= 100) {
            step = 10
            max = Math.ceil(yAxisMax / step) * step
        } else if (yAxisMax <= 500) {
            step = 50
            max = Math.ceil(yAxisMax / step) * step
        } else {
            step = 100
            max = Math.ceil(yAxisMax / step) * step
        }

        return {min: 0, max, step, unit}
    } else {
        // CC 攻击使用 RPS，可能需要转换为 M RPS
        let max: number
        let step: number
        let unit: string

        if (yAxisMax >= 1000000) {
            // 使用 M RPS 单位
            unit = 'M RPS'
            const maxInM = yAxisMax / 1000000
            step = Math.ceil(maxInM / 5)
            max = Math.ceil(maxInM / step) * step
        } else {
            // 使用 RPS 单位
            unit = 'RPS'
            if (yAxisMax <= 100000) {
                step = 10000
            } else {
                step = 50000
            }
            max = Math.ceil(yAxisMax / step) * step
        }

        return {min: 0, max, step, unit}
    }
}
