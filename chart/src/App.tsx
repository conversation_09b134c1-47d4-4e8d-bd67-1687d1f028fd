import {useState, useRef} from 'react'
import {AttackConfigForm} from './components/AttackConfigForm'
import {TrafficChart} from './components/TrafficChart'
import type {AttackConfig, TrafficDataPoint} from './types/attack'
import {generateTrafficData, calculateYAxisRange} from './lib/trafficGenerator'

function App() {
    const [chartData, setChartData] = useState<TrafficDataPoint[] | null>(null)
    const [currentConfig, setCurrentConfig] = useState<AttackConfig | null>(null)
    const [yAxisRange, setYAxisRange] = useState<{
        min: number
        max: number
        step: number
        unit: string
    } | null>(null)

    const chartRef = useRef<HTMLDivElement>(null)

    const handleGenerateChart = (config: AttackConfig) => {
        // 生成流量数据
        const data = generateTrafficData(config)

        // 计算 Y 轴范围
        const range = calculateYAxisRange(data, config.attackType)

        // 更新状态
        setChartData(data)
        setCurrentConfig(config)
        setYAxisRange(range)

        // 自动滚动到图表区域
        setTimeout(() => {
            chartRef.current?.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            })
        }, 100)
    }


    return (
        <div className="min-h-screen bg-gray-900">
            <div className="container mx-auto px-4 py-6 max-w-7xl">
                <header className="text-center mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-700 rounded-lg">
                        <svg className="w-8 h-8 text-gray-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                </header>

                <main className="space-y-8">
                    {/* 配置表单 */}
                    <div className="w-full">
                        <AttackConfigForm onGenerate={handleGenerateChart}/>
                    </div>

                    {/* 图表显示区域 */}
                    <div ref={chartRef} className="w-full">
                        {chartData && currentConfig && yAxisRange ? (
                            <TrafficChart
                                data={chartData}
                                config={currentConfig}
                                yAxisRange={yAxisRange}
                            />
                        ) : (
                            <div className="bg-gray-800 rounded-lg shadow-sm border border-gray-700 p-12">
                                <div className="text-center text-gray-400">
                                    <div className="mb-6">
                                        <div
                                            className="inline-flex items-center justify-center w-20 h-20 bg-gray-700 rounded-full">
                                            <svg
                                                className="h-10 w-10 text-gray-400"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={1.5}
                                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                                />
                                            </svg>
                                        </div>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-200 mb-3">
                                        准备生成滤前流量图表...
                                    </h3>
                                    <p className="text-gray-400 max-w-md mx-auto leading-relaxed">
                                        ATDS - 安全防御流量可视化系统
                                    </p>
                                    <div className="mt-6 flex items-center justify-center text-sm text-gray-500">
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                             viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        支持 DDoS 和 CC 攻击类型的滤前流量
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </main>

                {/* 页脚信息 */}
                <footer className="mt-16 pt-8 border-t border-gray-700">
                    <div className="text-center">
                        <p className="text-sm text-gray-400">
                            © 2025 ATDS - 安全防御流量可视化系统
                        </p>
                    </div>
                </footer>
            </div>
        </div>
    )
}

export default App
