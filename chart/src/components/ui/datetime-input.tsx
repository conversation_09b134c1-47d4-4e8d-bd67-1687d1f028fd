import * as React from "react"
import {cn} from "../../lib/utils"

export type DateTimeInputProps = React.InputHTMLAttributes<HTMLInputElement>

const DateTimeInput = React.forwardRef<HTMLInputElement, DateTimeInputProps>(
    ({className, ...props}, ref) => {
        return (
            <div className="relative">
                <input
                    type="datetime-local"
                    className={cn(
                        "flex h-10 w-full rounded-md border border-gray-600 bg-gray-700 text-gray-100 px-3 py-2 text-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-800 disabled:cursor-not-allowed disabled:opacity-50",
                        // 隐藏默认的日历图标
                        "[&::-webkit-calendar-picker-indicator]:opacity-0 [&::-webkit-calendar-picker-indicator]:absolute [&::-webkit-calendar-picker-indicator]:right-3 [&::-webkit-calendar-picker-indicator]:w-4 [&::-webkit-calendar-picker-indicator]:h-4 [&::-webkit-calendar-picker-indicator]:cursor-pointer",
                        className
                    )}
                    ref={ref}
                    {...props}
                />
                {/* 自定义日历图标 */}
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                    </svg>
                </div>
            </div>
        )
    }
)
DateTimeInput.displayName = "DateTimeInput"

export {DateTimeInput}
