import * as React from "react"
import {cn} from "../../lib/utils"

export interface RadioGroupProps {
    value?: string
    onValueChange?: (value: string) => void
    className?: string
    children: React.ReactNode
}

export interface RadioGroupItemProps {
    value: string
    id?: string
    className?: string
    children: React.ReactNode
    checked?: boolean
    onChange?: () => void
}

const RadioGroup = React.forwardRef<HTMLDivElement, RadioGroupProps>(
    ({className, value, onValueChange, children, ...props}, ref) => {
        return (
            <div
                ref={ref}
                className={cn("grid gap-2", className)}
                role="radiogroup"
                {...props}
            >
                {React.Children.map(children, (child) => {
                    if (React.isValidElement<RadioGroupItemProps>(child)) {
                        return React.cloneElement(child, {
                            ...child.props,
                            checked: child.props.value === value,
                            onChange: () => onValueChange?.(child.props.value),
                        })
                    }
                    return child
                })}
            </div>
        )
    }
)
RadioGroup.displayName = "RadioGroup"

const RadioGroupItem = React.forwardRef<HTMLInputElement, RadioGroupItemProps>(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ({className, children, value, id, ...props}, ref) => {
        return (
            <label
                htmlFor={id || value}
                className="relative flex items-center space-x-3 cursor-pointer group px-4 py-2 rounded-lg border border-gray-600 bg-gray-800 hover:bg-gray-700 hover:border-gray-500 transition-all duration-200 has-[:checked]:bg-blue-600 has-[:checked]:border-blue-500 has-[:checked]:shadow-lg has-[:checked]:shadow-blue-500/25"
            >
                <input
                    ref={ref}
                    type="radio"
                    id={id || value}
                    value={value}
                    className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-500 focus:outline-none focus:ring-0 focus-visible:outline-none"
                    onMouseDown={(e) => {
                        // 点击后立即移除焦点
                        setTimeout(() => {
                            (e.target as HTMLInputElement).blur()
                        }, 0)
                    }}
                    {...props}
                />
                <span
                    className="text-sm font-medium text-gray-200 group-hover:text-gray-100 group-has-[:checked]:text-white transition-colors duration-200">
          {children}
        </span>
            </label>
        )
    }
)
RadioGroupItem.displayName = "RadioGroupItem"

export {RadioGroup, RadioGroupItem}
