import * as React from "react"
import {cva, type VariantProps} from "class-variance-authority"
import {cn} from "../../lib/utils"

const buttonVariants = cva(
    "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
    {
        variants: {
            variant: {
                default: "bg-blue-600 text-white hover:bg-blue-700",
                destructive:
                    "bg-red-600 text-white hover:bg-red-700",
                outline:
                    "border border-gray-600 bg-gray-700 text-gray-100 hover:bg-gray-600",
                secondary:
                    "bg-gray-600 text-gray-100 hover:bg-gray-500",
                ghost: "text-gray-300 hover:bg-gray-700 hover:text-gray-100",
                link: "text-blue-400 underline-offset-4 hover:underline",
            },
            size: {
                default: "h-10 px-4 py-2",
                sm: "h-9 rounded-md px-3",
                lg: "h-11 rounded-md px-8",
                icon: "h-10 w-10",
            },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    }
)

export interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ({className, variant, size, asChild = false, ...props}, ref) => {
        return (
            <button
                className={cn(buttonVariants({variant, size, className}))}
                ref={ref}
                {...props}
            />
        )
    }
)
Button.displayName = "Button"

// eslint-disable-next-line react-refresh/only-export-components
export {Button, buttonVariants}
