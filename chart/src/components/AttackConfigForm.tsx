import {useState} from 'react'
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from './ui/card'
import {Button} from './ui/button'
import {Input} from './ui/input'
import {RadioGroup, RadioGroupItem} from './ui/radio-group'
import {DateTimeInput} from './ui/datetime-input'
import type {AttackConfig, AttackType, FluctuationLevel} from '../types/attack'
import {ATTACK_TYPES, FLUCTUATION_LEVELS} from '../types/attack'
import {getDefaultTimeConfig} from '../lib/utils'
import * as React from "react";

interface AttackConfigFormProps {
    onGenerate: (config: AttackConfig) => void
}

export function AttackConfigForm({onGenerate}: AttackConfigFormProps) {
    const defaultTimes = getDefaultTimeConfig()

    const [config, setConfig] = useState<AttackConfig>({
        attackType: 'DDoS',
        peakTraffic: 100,
        fluctuationLevel: 'medium',
        ...defaultTimes
    })

    const [errors, setErrors] = useState<Record<string, string>>({})

    const handleAttackTypeChange = (attackType: AttackType) => {
        const newConfig = {...config, attackType}

        // 根据攻击类型设置默认峰值
        if (attackType === 'DDoS') {
            newConfig.peakTraffic = 100
            delete newConfig.domain
        } else {
            newConfig.peakTraffic = 50000
            newConfig.domain = 'yphzk.yunphui.com'
        }

        setConfig(newConfig)
    }

    const validateConfig = (): boolean => {
        const newErrors: Record<string, string> = {}

        // 验证峰值流量
        const attackTypeConfig = ATTACK_TYPES[config.attackType]
        if (config.peakTraffic < attackTypeConfig.minValue) {
            newErrors.peakTraffic = `${config.attackType} 攻击流量不能低于 ${attackTypeConfig.minValue} ${attackTypeConfig.unit}`
        }
        if (config.peakTraffic > attackTypeConfig.maxValue) {
            newErrors.peakTraffic = `${config.attackType} 攻击流量不能超过 ${attackTypeConfig.maxValue} ${attackTypeConfig.unit}`
        }

        // 验证域名 (CC 攻击)
        if (config.attackType === 'CC' && !config.domain?.trim()) {
            newErrors.domain = 'CC 攻击需要指定域名'
        }

        // 验证时间配置
        const timeDiff = config.chartEndTime.getTime() - config.chartStartTime.getTime()
        if (timeDiff > 24 * 60 * 60 * 1000) {
            newErrors.time = '图表时间跨度不能超过 24 小时'
        }

        if (config.attackStartTime >= config.attackEndTime) {
            newErrors.attackTime = '攻击开始时间必须早于结束时间'
        }

        if (config.attackStartTime < config.chartStartTime || config.attackEndTime > config.chartEndTime) {
            newErrors.attackTime = '攻击时间必须在图表时间范围内'
        }

        // 验证峰值时间
        if (config.peakTime < config.attackStartTime) {
            newErrors.peakTime = '峰值时间不能早于攻击开始时间'
        }

        const maxPeakTime = new Date(config.attackStartTime)
        maxPeakTime.setMinutes(config.attackStartTime.getMinutes() + 30)
        if (config.peakTime > maxPeakTime) {
            newErrors.peakTime = '峰值时间不能超过攻击开始时间后30分钟'
        }

        if (config.peakTime > config.attackEndTime) {
            newErrors.peakTime = '峰值时间不能晚于攻击结束时间'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        if (validateConfig()) {
            onGenerate(config)
        }
    }

    const formatDateTimeLocal = (date: Date): string => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        return `${year}-${month}-${day}T${hours}:${minutes}`
    }

    const parseDateTime = (dateTimeString: string): Date => {
        return new Date(dateTimeString)
    }

    return (
        <>
            <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                    <CardTitle className="text-gray-100">流量配置</CardTitle>
                    <CardDescription className="text-gray-400">
                        从数据中心防火墙生成滤前流量图
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* 攻击类型选择 */}
                        <div className="space-y-3">
                            <label className="text-sm font-medium text-gray-200">攻击类型</label>
                            <RadioGroup
                                value={config.attackType}
                                onValueChange={(value) => handleAttackTypeChange(value as AttackType)}
                                className="flex flex-row space-x-6"
                            >
                                {Object.entries(ATTACK_TYPES).map(([key, value]) => (
                                    <RadioGroupItem key={key} value={key}>
                                        {value.label}
                                    </RadioGroupItem>
                                ))}
                            </RadioGroup>
                        </div>

                        {/* CC 攻击域名配置 */}
                        {config.attackType === 'CC' && (
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-200">目标域名</label>
                                <Input
                                    type="text"
                                    value={config.domain || ''}
                                    onChange={(e) => setConfig({...config, domain: e.target.value})}
                                    placeholder="请输入目标域名, 如：example.com"
                                />

                            </div>
                        )}

                        {/* 流量峰值配置 */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-200">
                                滤前流量峰值 ({ATTACK_TYPES[config.attackType].unit})
                            </label>
                            <Input
                                type="number"
                                value={config.peakTraffic}
                                onChange={(e) => setConfig({...config, peakTraffic: Number(e.target.value)})}
                                min={ATTACK_TYPES[config.attackType].minValue}
                                max={ATTACK_TYPES[config.attackType].maxValue}
                            />

                            <p className="text-xs text-gray-400">
                                范围: {ATTACK_TYPES[config.attackType].minValue} - {ATTACK_TYPES[config.attackType].maxValue} {ATTACK_TYPES[config.attackType].unit}
                            </p>
                        </div>

                        {/* 流量波动程度 */}
                        <div className="space-y-3">
                            <label className="text-sm font-medium text-gray-200">流量波动程度</label>
                            <RadioGroup
                                value={config.fluctuationLevel}
                                onValueChange={(value) => setConfig({
                                    ...config,
                                    fluctuationLevel: value as FluctuationLevel
                                })}
                                className="flex flex-row space-x-4"
                            >
                                {Object.entries(FLUCTUATION_LEVELS).map(([key, value]) => (
                                    <RadioGroupItem key={key} value={key}>
                                        {value.label}
                                    </RadioGroupItem>
                                ))}
                            </RadioGroup>
                        </div>

                        {/* 时间配置 */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-200">图表开始时间</label>
                                <DateTimeInput
                                    value={formatDateTimeLocal(config.chartStartTime)}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        chartStartTime: parseDateTime(e.target.value)
                                    })}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-200">图表结束时间</label>
                                <DateTimeInput
                                    value={formatDateTimeLocal(config.chartEndTime)}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        chartEndTime: parseDateTime(e.target.value)
                                    })}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-200">攻击开始时间</label>
                                <DateTimeInput
                                    value={formatDateTimeLocal(config.attackStartTime)}
                                    onChange={(e) => {
                                        const newAttackStartTime = parseDateTime(e.target.value)
                                        // 自动调整峰值时间为攻击开始时间+15分钟
                                        const newPeakTime = new Date(newAttackStartTime)
                                        newPeakTime.setMinutes(newAttackStartTime.getMinutes() + 15)
                                        setConfig({
                                            ...config,
                                            attackStartTime: newAttackStartTime,
                                            peakTime: newPeakTime
                                        })
                                    }}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-200">攻击结束时间</label>
                                <DateTimeInput
                                    value={formatDateTimeLocal(config.attackEndTime)}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        attackEndTime: parseDateTime(e.target.value)
                                    })}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-200">峰值时间</label>
                                <DateTimeInput
                                    value={formatDateTimeLocal(config.peakTime)}
                                    onChange={(e) => setConfig({...config, peakTime: parseDateTime(e.target.value)})}
                                />
                            </div>
                        </div>

                        {/* 提交按钮 */}
                        <Button type="submit" className="w-full">
                            生成流量图表
                        </Button>
                    </form>
                </CardContent>
            </Card>

            {/* 独立的提示/错误提示容器 */}
            {(errors.peakTraffic || errors.domain || errors.time || errors.attackTime || errors.peakTime) && (
                <Card className="bg-red-900/20 border-red-700/50 mt-4">
                    <CardContent className="pt-6">
                        <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor"
                                     viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <div className="flex-1">
                                <h3 className="text-sm font-medium text-red-300 mb-2">配置错误</h3>
                                <div className="space-y-1">
                                    {errors.peakTraffic && <p className="text-sm text-red-200">{errors.peakTraffic}</p>}
                                    {errors.domain && <p className="text-sm text-red-200">{errors.domain}</p>}
                                    {errors.time && <p className="text-sm text-red-200">{errors.time}</p>}
                                    {errors.attackTime && <p className="text-sm text-red-200">{errors.attackTime}</p>}
                                    {errors.peakTime && <p className="text-sm text-red-200">{errors.peakTime}</p>}
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </>
    )
}
