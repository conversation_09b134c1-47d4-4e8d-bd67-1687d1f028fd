import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    Filler,
    type ChartOptions
} from 'chart.js'
import {Line} from 'react-chartjs-2'
import type {AttackConfig, TrafficDataPoint} from '../types/attack'
import {formatTime} from '../lib/utils'
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from './ui/card'

// 注册 Chart.js 组件
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    Filler
)

interface TrafficChartProps {
    data: TrafficDataPoint[]
    config: AttackConfig
    yAxisRange: {
        min: number
        max: number
        step: number
        unit: string
    }
}

export function TrafficChart({data, config, yAxisRange}: TrafficChartProps) {
    // 准备图表数据
    const chartData = {
        labels: data.map(point => formatTime(point.time)),
        datasets: [
            {
                label: `${config.attackType} 攻击滤前流量`,
                data: data.map(point => {
                    // 如果是 CC 攻击且单位是 M RPS，需要转换显示值
                    if (config.attackType === 'CC' && yAxisRange.unit === 'M RPS') {
                        return point.value / 1000000
                    }
                    return point.value
                }),
                borderColor: config.attackType === 'DDoS' ? '#dc2626' : '#ea580c',
                backgroundColor: config.attackType === 'DDoS' ? 'rgba(220, 38, 38, 0.2)' : 'rgba(234, 88, 12, 0.2)',
                fill: true,
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 6,
                pointBackgroundColor: config.attackType === 'DDoS' ? '#dc2626' : '#ea580c',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
            }
        ]
    }

    // 图表配置选项
    const options: ChartOptions<'line'> = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
                labels: {
                    font: {
                        size: 12,
                        family: 'Inter, system-ui, sans-serif'
                    },
                    color: '#d1d5db'
                }
            },
            title: {
                display: false
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#e5e7eb',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: true,
                callbacks: {
                    label: function (context) {
                        const value = context.parsed.y
                        const unit = yAxisRange.unit
                        return `${context.dataset.label}: ${value.toFixed(2)} ${unit}`
                    },
                    afterLabel: function (context) {
                        const currentTime = data[context.dataIndex]?.time
                        const currentValue = context.parsed.y
                        const result = []

                        // 检查是否是峰值点（时间匹配且数值等于峰值）
                        const isPeakPoint = currentTime &&
                            Math.abs(currentTime.getTime() - config.peakTime.getTime()) < 60000 && // 1分钟误差范围
                            Math.abs(currentValue - config.peakTraffic) < 1 // 数值误差范围

                        if (isPeakPoint) {
                            // 如果是峰值点，显示峰值信息
                            result.push('')
                            result.push(`🎯 峰值时间: ${formatTime(config.peakTime)}`)
                            result.push(`🎯 峰值流量: ${config.peakTraffic} ${yAxisRange.unit}`)
                        } else if (currentTime &&
                            currentTime >= config.attackStartTime &&
                            currentTime <= config.attackEndTime) {
                            // 如果是攻击期间的普通点，只显示攻击状态
                            result.push('')
                            result.push('攻击进行中')
                        }

                        return result
                    }
                }
            }
        },
        interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: config.chartStartTime.toLocaleDateString('en-CA'), // 使用本地时间，格式为 YYYY-MM-DD
                    font: {
                        size: 12,
                        weight: 'bold'
                    },
                    color: '#d1d5db'
                },
                grid: {
                    color: '#374151',
                    lineWidth: 1
                },
                ticks: {
                    color: '#9ca3af',
                    font: {
                        size: 11
                    },
                    maxRotation: 45,
                    minRotation: 0
                }
            },
            y: {
                display: true,
                title: {
                    display: true,
                    text: `滤前流量 (${yAxisRange.unit})`,
                    font: {
                        size: 12,
                        weight: 'bold'
                    },
                    color: '#d1d5db'
                },
                min: yAxisRange.min,
                max: yAxisRange.max,
                grid: {
                    color: '#374151',
                    lineWidth: 1
                },
                ticks: {
                    stepSize: yAxisRange.step,
                    color: '#9ca3af',
                    font: {
                        size: 11
                    },
                    callback: function (value) {
                        return `${value} ${yAxisRange.unit}`
                    }
                }
            }
        },
        elements: {
            line: {
                borderWidth: 2
            },
            point: {
                hoverBorderWidth: 3
            }
        }
    }

    // 生成图表标题和描述
    const chartTitle = `${config.attackType} 攻击滤前流量图`
    const chartDescription = config.domain ? `CC攻击的目标域名: ${config.domain}` : ''

    return (
        <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
                <CardTitle className="text-gray-100">{chartTitle}</CardTitle>
                <CardDescription className="text-sm text-gray-400">
                    {chartDescription}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="h-96 w-full">
                    <Line data={chartData} options={options}/>
                </div>
            </CardContent>
        </Card>
    )
}
